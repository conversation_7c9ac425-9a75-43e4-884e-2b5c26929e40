package com.shuyi.discipline.domain.usecase

import android.content.Context
import androidx.work.*
import com.shuyi.discipline.domain.worker.SystemMonitorWorker
import timber.log.Timber
import java.util.concurrent.TimeUnit

/**
 * 系统监控调度用例
 * 负责调度定期的系统监控任务
 */
class ScheduleSystemMonitorUseCase(
    private val context: Context
) {

    /**
     * 开始系统监控
     */
    fun startMonitoring() {
        val workManager = WorkManager.getInstance(context)

        // 取消现有的监控任务
        workManager.cancelUniqueWork(WORK_NAME)

        // 🎯 修复：创建更宽松的监控任务约束，减少系统限制影响
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED) // 不需要网络
            .setRequiresBatteryNotLow(false) // 允许低电量时运行
            .setRequiresCharging(false) // 不需要充电
            .setRequiresDeviceIdle(false) // 不需要设备空闲
            .setRequiresStorageNotLow(false) // 不需要存储空间充足
            .build()

        // 🎯 修复：调整监控频率，减少系统限制的影响
        // 从每2分钟改为每5分钟，减少被系统限制的可能性
        val monitorRequest = PeriodicWorkRequestBuilder<SystemMonitorWorker>(
            5L, TimeUnit.MINUTES, // 重复间隔：5分钟
            2L, TimeUnit.MINUTES  // 灵活间隔：2分钟
        )
            .setConstraints(constraints)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()

        // 入队监控任务
        workManager.enqueueUniquePeriodicWork(
            WORK_NAME,
            ExistingPeriodicWorkPolicy.REPLACE,
            monitorRequest
        )

        Timber.d("系统监控任务已启动，每5分钟执行一次（减少系统限制影响）")
    }

    /**
     * 停止系统监控
     */
    fun stopMonitoring() {
        val workManager = WorkManager.getInstance(context)
        workManager.cancelUniqueWork(WORK_NAME)
        Timber.d("系统监控任务已停止")
    }

    /**
     * 立即执行一次监控检查
     */
    fun executeImmediateCheck() {
        val workManager = WorkManager.getInstance(context)

        // 创建一次性监控任务
        val immediateRequest = OneTimeWorkRequestBuilder<SystemMonitorWorker>()
            .setExpedited(OutOfQuotaPolicy.RUN_AS_NON_EXPEDITED_WORK_REQUEST)
            .build()

        // 入队一次性任务
        workManager.enqueueUniqueWork(
            IMMEDIATE_WORK_NAME,
            ExistingWorkPolicy.REPLACE,
            immediateRequest
        )

        Timber.d("立即执行系统监控检查")
    }

    /**
     * 检查监控任务状态
     */
    fun getMonitoringStatus(): Boolean {
        val workManager = WorkManager.getInstance(context)
        val workInfos = workManager.getWorkInfosForUniqueWork(WORK_NAME)

        return try {
            val workInfo = workInfos.get().firstOrNull()
            val isRunning = workInfo?.state == WorkInfo.State.RUNNING || workInfo?.state == WorkInfo.State.ENQUEUED
            Timber.d("系统监控任务状态: ${workInfo?.state}, 运行中: $isRunning")
            isRunning
        } catch (e: Exception) {
            Timber.e(e, "获取监控任务状态失败")
            false
        }
    }

    companion object {
        private const val WORK_NAME = "system_monitor_periodic"
        private const val IMMEDIATE_WORK_NAME = "system_monitor_immediate"
    }
}
